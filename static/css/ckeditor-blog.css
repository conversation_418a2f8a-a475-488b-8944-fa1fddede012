/* CKEditor Blog-Specific Styling for Novustell Travel */

/* Import base styles */
@import url('ckeditor-custom.css');

/* Blog-specific enhancements */
body {
    font-size: 16px;
    line-height: 1.7;
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
}

/* Enhanced blog headings */
h1 {
    font-size: 2.5em;
    color: #0f238d;
    border-bottom: 3px solid #ff9d00;
    padding-bottom: 0.5em;
    margin-bottom: 1em;
}

h2 {
    font-size: 2em;
    color: #0f238d;
    margin-top: 2em;
    margin-bottom: 1em;
    position: relative;
}

h2::before {
    content: '';
    position: absolute;
    left: -20px;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 100%;
    background: #ff9d00;
    border-radius: 2px;
}

h3 {
    color: #0f238d;
    margin-top: 1.5em;
    margin-bottom: 0.75em;
}

/* Blog paragraphs */
p {
    margin-bottom: 1.2em;
    text-align: left;
}

/* Enhanced blockquotes for travel quotes */
blockquote {
    border-left: 5px solid #ff9d00;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    margin: 2em 0;
    padding: 1.5em 2em;
    border-radius: 0 10px 10px 0;
    position: relative;
    font-size: 1.1em;
    line-height: 1.6;
}

blockquote::before {
    content: '"';
    font-size: 4em;
    color: #ff9d00;
    position: absolute;
    top: -10px;
    left: 15px;
    font-family: Georgia, serif;
}

blockquote p {
    margin: 0;
    font-style: italic;
    color: #333;
}

/* Travel-specific content blocks */
.destination-highlight {
    background: linear-gradient(135deg, #0f238d 0%, #1a3a9e 100%);
    color: white;
    padding: 1.5em;
    border-radius: 10px;
    margin: 2em 0;
    box-shadow: 0 4px 15px rgba(15, 35, 141, 0.3);
}

.destination-highlight h3 {
    color: #ff9d00;
    margin-top: 0;
}

.travel-tip {
    background: linear-gradient(135deg, #fff3e0 0%, #ffe0b3 100%);
    border-left: 5px solid #ff9d00;
    padding: 1.5em;
    margin: 2em 0;
    border-radius: 0 10px 10px 0;
    box-shadow: 0 2px 10px rgba(255, 157, 0, 0.2);
}

.travel-tip::before {
    content: "💡 Pro Tip: ";
    font-weight: 700;
    color: #0f238d;
    display: block;
    margin-bottom: 0.5em;
}

/* Enhanced lists for travel content */
ul.travel-checklist {
    list-style: none;
    padding-left: 0;
}

ul.travel-checklist li {
    position: relative;
    padding-left: 2em;
    margin-bottom: 0.75em;
}

ul.travel-checklist li::before {
    content: "✈️";
    position: absolute;
    left: 0;
    top: 0;
}

ol.itinerary {
    counter-reset: day-counter;
    list-style: none;
    padding-left: 0;
}

ol.itinerary li {
    counter-increment: day-counter;
    position: relative;
    padding: 1em 1em 1em 3em;
    margin-bottom: 1em;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #0f238d;
}

ol.itinerary li::before {
    content: "Day " counter(day-counter);
    position: absolute;
    left: 1em;
    top: 1em;
    font-weight: 700;
    color: #0f238d;
    font-size: 0.9em;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Enhanced images for blog */
img {
    border-radius: 12px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    margin: 2em 0;
    transition: transform 0.3s ease;
}

img:hover {
    transform: scale(1.02);
}

/* Image captions */
.image-caption {
    text-align: center;
    font-style: italic;
    color: #666;
    font-size: 0.9em;
    margin-top: 0.5em;
    margin-bottom: 2em;
}

/* Call-to-action boxes */
.cta-box {
    background: linear-gradient(135deg, #ff9d00 0%, #ffb84d 100%);
    color: white;
    padding: 2em;
    border-radius: 15px;
    text-align: center;
    margin: 2em 0;
    box-shadow: 0 8px 25px rgba(255, 157, 0, 0.3);
}

.cta-box h3 {
    color: white;
    margin-top: 0;
    margin-bottom: 1em;
}

.cta-box p {
    margin-bottom: 1.5em;
    font-size: 1.1em;
}

/* Tables for travel information */
table.travel-info {
    border: none;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    border-radius: 10px;
    overflow: hidden;
}

table.travel-info th {
    background: linear-gradient(135deg, #0f238d 0%, #1a3a9e 100%);
    color: white;
    padding: 1em;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

table.travel-info td {
    padding: 1em;
    border-bottom: 1px solid #e9ecef;
}

table.travel-info tr:last-child td {
    border-bottom: none;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    body {
        padding: 15px;
        font-size: 14px;
    }
    
    h1 {
        font-size: 2em;
    }
    
    h2 {
        font-size: 1.6em;
    }
    
    blockquote {
        padding: 1em 1.5em;
        margin: 1.5em 0;
    }
    
    .destination-highlight,
    .travel-tip,
    .cta-box {
        padding: 1.25em;
        margin: 1.5em 0;
    }
}
