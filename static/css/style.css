*{
    margin:0;
    padding:0;
}
:root {
    --animate-duration: 800ms;
    --animate-delay: 0.9s;
  }
  .btn-primary {
    background-color: #0f238d; /* Replace with your desired color */
}
.header{
    height:100%;
    background-image:linear-gradient(rgba(0,0,0,0.6),rgba(0,0,0,0.6)),url(../images/Maldives.jpg);
    background-size:cover;
    background-position: center;
    background-attachment: fixed;
    padding-top: 20px;
    text-align:center;
    color:#fff;
}
.header img{
    width:75px;
    float:left;
    margin-top:-15px;
}
.navbar img{
    width:75px;
    float:left;
    margin-top:-15px;
}


.login-btn{
    width:100px;
    padding:8px 0;
    outline:none !important;
    border:2px solid #fff;
    border-radius: 50px;
    background: transparent;
    color: #fff;
    float:right;
}

.header h1{
    padding-top:260px;
    padding-bottom: 0;
    font-size:55px;
}
.header p{
  margin:18px 0;  
}

.price-dest{
    float:right; 
    background:linear-gradient(#8537ce,#cf4a98);
    padding:10px; 
    border-radius:30px;
    height:45px;
    width:125px;
    text-align:center;
    border-style:inset;
    border-color: grey;
    color:white;
    margin:0 10px;
}
.icons a{
    text-decoration:none;
    color:white
}
.input-group{
    width:90%;
    max-width:500px;
    border-radius:30px;
    margin:auto;
    background: #fff;
    padding:2px;
}
.form-control{
    border:0 !important;
    border-radius:30px!important;
    margin:2px;
    box-shadow:none !important;
}
.input-group-text
{
    width:100px;
    background-image:linear-gradient(#00ff7e,#1f3d90);
    border:0 !important;
    color:#fff !important;
    padding:0 25px !important;
    border-radius:30px  !important;
    box-shadow:none  !important;
}
.packages{
    padding:100px 0;
    
}

h1{
    text-align:center;
    padding-bottom:30px;
}
.package-img img{
    width:100%;
}
.price{
    width:100px; 
    height:50px;
    background:#ff5722;
    color:#fff;
    font-weight:600;
    border-radius: 40%;
    padding:10px;
    text-align:center;
    box-shadow: 0 0 10px 1px rgba(37,73,214,0.18);
    position: absolute;
    right:20px;
    bottom:-25px;
}
.package-img{
    position:relative;
}

.packages .fa{
    font-size:20px;
    color:#ff5722;
}
.package-details
{
    padding:20px;
    text-align:justify;
}
.package-details h4{
    font-weight: 600;
    margin-top: 20px;
}
.package-details .fa{
    margin-right:10px;
}
.package-box{
    box-shadow:0 0 10px 1px rgba(37,73,214,0.18);
    margin-bottom:30px;
   
}

.gallery{
    padding:100px 0;
    background:#efefef;
}
.gallery-box img{
    width:100%;
    border-radius:10px;
    cursor:pointer;
    transition:1s;
}
.gallery-box img:hover{
    transform:scale(1.5);
}
.gallery-box h4{
    display:block;
    position:absolute;
    font-weight:600;
    top:50%;
    left:50%;
    color:#fff;
    font-size:20px;
    text-shadow:-2px 2px 1px #000;
    transform:translate(-50%,-50%);
}
.gallery-box{
    position: relative;
    margin-bottom:30px;
}
.footer
{
    text-align:center;
    padding:100px 20px;
    background-image:linear-gradient(#9610fb,#2d557d);
    color:#fff;
}
.footer-logo{
    
    width:120px;
    margin-top:-75px;
    margin-bottom:15px;
    text-align:center;
}
.footer h4{
    text-align:center;
    margin-top:15px;
    margin-bottom:25px;
}
.footer p{
    text-align:center;
    font-size:20px;
}
.footer .row .fa{
    padding-right:20px;
    font-size:20px;
}
.pull-up{
    transition: transform .2s; 
    border-radius: 10px;
    box-shadow: 2px 2px 5px rgb(22, 5, 51);
}
.pull-up:hover{
    transform: scale(1.1);
    box-shadow: 2px 2px 10px rgb(22, 5, 51);
}