/* CKEditor Custom Styling for Novustell Travel */

/* Custom font loading */
@font-face {
    font-family: 'Fonarto';
    font-style: normal;
    font-weight: 400;
    src: url('../fonts/FonartoRegular-8Mon2.woff') format('woff');
    font-display: swap;
}

@font-face {
    font-family: 'Fonarto';
    font-style: normal;
    font-weight: 700;
    src: url('../fonts/FonartoBold-RpYOo.woff') format('woff');
    font-display: swap;
}

/* Base styling for CKEditor content */
body {
    font-family: 'Fonarto', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background: #fff;
    margin: 15px;
}

/* Headings with Novustell branding */
h1, h2, h3, h4, h5, h6 {
    font-family: 'Fonarto', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    font-weight: 700;
    color: #0f238d;
    margin-top: 1.5em;
    margin-bottom: 0.75em;
    line-height: 1.3;
}

h1 { font-size: 2.2em; }
h2 { font-size: 1.8em; }
h3 { font-size: 1.5em; }
h4 { font-size: 1.3em; }
h5 { font-size: 1.1em; }
h6 { font-size: 1em; }

/* Paragraphs */
p {
    margin-bottom: 1em;
    text-align: justify;
}

/* Links */
a {
    color: #0f238d;
    text-decoration: none;
    transition: color 0.3s ease;
}

a:hover {
    color: #ff9d00;
    text-decoration: underline;
}

/* Lists */
ul, ol {
    margin: 1em 0;
    padding-left: 2em;
}

li {
    margin-bottom: 0.5em;
}

/* Blockquotes */
blockquote {
    border-left: 4px solid #ff9d00;
    margin: 1.5em 0;
    padding: 1em 1.5em;
    background: #f8f9fa;
    font-style: italic;
    color: #555;
}

blockquote p {
    margin: 0;
}

/* Tables */
table {
    width: 100%;
    border-collapse: collapse;
    margin: 1.5em 0;
    border: 1px solid #dee2e6;
}

th, td {
    padding: 0.75em;
    text-align: left;
    border: 1px solid #dee2e6;
}

th {
    background-color: #0f238d;
    color: white;
    font-weight: 700;
}

tr:nth-child(even) {
    background-color: #f8f9fa;
}

/* Images */
img {
    max-width: 100%;
    height: auto;
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    margin: 1em 0;
}

/* Custom Novustell styles */
.novustell-highlight {
    background-color: #ff9d00;
    color: white;
    padding: 2px 8px;
    border-radius: 4px;
    font-weight: 600;
}

.novustell-primary {
    color: #0f238d;
    font-weight: 700;
}

.travel-tip {
    background-color: #e8f4fd;
    border-left: 4px solid #0f238d;
    padding: 1em 1.5em;
    margin: 1.5em 0;
    border-radius: 0 8px 8px 0;
}

.travel-tip::before {
    content: "💡 Travel Tip: ";
    font-weight: 700;
    color: #0f238d;
}

/* Code blocks */
pre {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 1em;
    overflow-x: auto;
    font-family: 'Courier New', monospace;
}

code {
    background: #f8f9fa;
    padding: 0.2em 0.4em;
    border-radius: 3px;
    font-family: 'Courier New', monospace;
    font-size: 0.9em;
}

/* Horizontal rules */
hr {
    border: none;
    height: 2px;
    background: linear-gradient(90deg, #0f238d, #ff9d00);
    margin: 2em 0;
    border-radius: 1px;
}

/* Strong and emphasis */
strong, b {
    font-weight: 700;
    color: #0f238d;
}

em, i {
    font-style: italic;
    color: #555;
}

/* Selection styling */
::selection {
    background: #ff9d00;
    color: white;
}

::-moz-selection {
    background: #ff9d00;
    color: white;
}
