{% extends 'users/basemain.html' %}
{% load static %}

{% block content %}
<!-- Hero Section -->
<section class="blog-hero-section" style="
    background: linear-gradient(135deg, rgba(15, 35, 141, 0.9) 0%, rgba(255, 157, 0, 0.8) 100%),
                url('{% static "assets/images/bg/page-bg.jpg" %}');
    background-size: cover;
    background-position: center;
    padding: 120px 0 80px;
    position: relative;
">
    <div class="container">
        <div class="row justify-content-center text-center">
            <div class="col-lg-8">
                <div class="hero-content text-white">
                    <h1 class="hero-title mb-4">Travel Stories & Insights</h1>
                    <p class="hero-subtitle mb-4">Discover amazing destinations, travel tips, and inspiring stories from around the world</p>
                    <div class="hero-stats d-flex justify-content-center gap-4 flex-wrap">
                        <div class="stat-item">
                            <span class="stat-number">{{ blog_count.count }}+</span>
                            <span class="stat-label">Articles</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number">{{ categories.count }}+</span>
                            <span class="stat-label">Categories</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Blog Content Section -->
<section class="blog-content-section py-5" style="background: linear-gradient(to bottom, #f8f9fa 0%, #ffffff 100%);">
    <div class="container">
        <!-- Search and Filter Bar -->
        <div class="row mb-5">
            <div class="col-12">
                <div class="search-filter-bar bg-white p-4 rounded-3 shadow-sm">
                    <div class="row align-items-center">
                        <div class="col-lg-6 mb-3 mb-lg-0">
                            <form method="GET" class="search-form">
                                <div class="input-group">
                                    <input type="text" class="form-control" name="q" value="{{ query }}"
                                           placeholder="Search articles, destinations, tips..."
                                           style="border-radius: 25px 0 0 25px; border: 2px solid #e9ecef;">
                                    <button class="btn btn-primary" type="submit"
                                            style="border-radius: 0 25px 25px 0; background: #0f238d; border-color: #0f238d;">
                                        <i class="fas fa-search"></i>
                                    </button>
                                </div>
                            </form>
                        </div>
                        <div class="col-lg-6">
                            <div class="filter-options d-flex justify-content-lg-end gap-2 flex-wrap">
                                <div class="dropdown">
                                    <button class="btn btn-outline-secondary dropdown-toggle" type="button"
                                            data-bs-toggle="dropdown" aria-expanded="false">
                                        <i class="fas fa-filter me-2"></i>Categories
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item" href="{% url 'blog:blog-list' %}">All Categories</a></li>
                                        {% for c in categories %}
                                        <li><a class="dropdown-item" href="{% url 'blog:category_detail' c.slug %}">{{ c.title }}</a></li>
                                        {% endfor %}
                                    </ul>
                                </div>
                                <button class="btn btn-outline-secondary" onclick="toggleView()">
                                    <i class="fas fa-th-large me-2"></i><span id="view-text">Grid</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Featured Posts Section -->
        {% if featured_blog %}
        <div class="row mb-5">
            <div class="col-12">
                <h3 class="section-heading mb-4">
                    <i class="fas fa-star text-warning me-2"></i>Featured Articles
                </h3>
                <div class="featured-posts-slider">
                    <div class="row">
                        {% for fp in featured_blog|slice:":3" %}
                        <div class="col-lg-4 col-md-6 mb-4">
                            <article class="featured-post-card">
                                <div class="post-image">
                                    <a href="{% url 'blog:blog-detail' fp.pid %}">
                                        <img src="{{ fp.image.cdn_url }}" alt="{{ fp.title }}" class="img-fluid">
                                        <div class="post-overlay">
                                            <span class="featured-badge">Featured</span>
                                        </div>
                                    </a>
                                </div>
                                <div class="post-content">
                                    <div class="post-meta mb-2">
                                        <span class="category-tag">{{ fp.category.title }}</span>
                                        <span class="read-time">{{ fp.get_read_time }} min read</span>
                                    </div>
                                    <h4 class="post-title">
                                        <a href="{% url 'blog:blog-detail' fp.pid %}">{{ fp.title|truncatechars:60 }}</a>
                                    </h4>
                                </div>
                            </article>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Main Blog Posts Grid -->
        <div class="row">
            <div class="col-lg-8">
                <div class="blog-posts-container" id="blog-posts-grid">
                    <div class="row" id="posts-container">
                        {% for p in blog %}
                        <div class="col-lg-6 col-md-6 mb-4 blog-post-item">
                            <article class="modern-blog-card">
                                <div class="post-image-wrapper">
                                    <a href="{% url 'blog:blog-detail' p.pid %}">
                                        <img src="{{ p.image.cdn_url }}" alt="{{ p.title }}" class="post-image">
                                        <div class="image-overlay">
                                            <div class="overlay-content">
                                                <i class="fas fa-arrow-right"></i>
                                            </div>
                                        </div>
                                    </a>
                                    {% if p.category %}
                                    <span class="category-badge">{{ p.category.title }}</span>
                                    {% endif %}
                                </div>
                                <div class="post-content">
                                    <div class="post-meta">
                                        <div class="meta-item">
                                            <i class="far fa-calendar-alt"></i>
                                            <span>{{ p.date|date:"M d, Y" }}</span>
                                        </div>
                                        <div class="meta-item">
                                            <i class="far fa-clock"></i>
                                            <span>{{ p.get_read_time }} min read</span>
                                        </div>
                                        <div class="meta-item">
                                            <i class="far fa-eye"></i>
                                            <span>{{ p.views }} views</span>
                                        </div>
                                    </div>
                                    <h3 class="post-title">
                                        <a href="{% url 'blog:blog-detail' p.pid %}">{{ p.title|truncatechars:80 }}</a>
                                    </h3>
                                    <div class="post-excerpt">
                                        {{ p.get_excerpt }}
                                    </div>
                                    <div class="post-footer">
                                        <div class="author-info">
                                            <i class="far fa-user"></i>
                                            <span>{{ p.user.first_name|default:p.user.username }}</span>
                                        </div>
                                        <a href="{% url 'blog:blog-detail' p.pid %}" class="read-more-btn">
                                            Read More <i class="fas fa-arrow-right ms-1"></i>
                                        </a>
                                    </div>
                                </div>
                            </article>
                        </div>
                        {% empty %}
                        <div class="col-12">
                            <div class="no-posts-found text-center py-5">
                                <i class="fas fa-search fa-3x text-muted mb-3"></i>
                                <h4 class="text-muted">No Articles Found</h4>
                                <p class="text-muted">Try adjusting your search criteria or browse all categories.</p>
                                <a href="{% url 'blog:blog-list' %}" class="btn btn-primary">View All Articles</a>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>

                <!-- Enhanced Pagination -->
                {% if blog.has_other_pages %}
                <div class="modern-pagination mt-5">
                    <nav aria-label="Blog pagination">
                        <ul class="pagination justify-content-center">
                            {% if blog.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="{% url 'blog:blog-list' %}?page=1{% if query %}&q={{ query }}{% endif %}"
                                   aria-label="First">
                                    <i class="fas fa-angle-double-left"></i>
                                </a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="{% url 'blog:blog-list' %}?page={{ blog.previous_page_number }}{% if query %}&q={{ query }}{% endif %}"
                                   aria-label="Previous">
                                    <i class="fas fa-angle-left"></i>
                                </a>
                            </li>
                            {% endif %}

                            {% for page_number in blog.paginator.page_range %}
                                {% if page_number == blog.number %}
                                <li class="page-item active">
                                    <span class="page-link">{{ page_number }}</span>
                                </li>
                                {% elif page_number > blog.number|add:'-3' and page_number < blog.number|add:'3' %}
                                <li class="page-item">
                                    <a class="page-link" href="{% url 'blog:blog-list' %}?page={{ page_number }}{% if query %}&q={{ query }}{% endif %}">
                                        {{ page_number }}
                                    </a>
                                </li>
                                {% endif %}
                            {% endfor %}

                            {% if blog.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="{% url 'blog:blog-list' %}?page={{ blog.next_page_number }}{% if query %}&q={{ query }}{% endif %}"
                                   aria-label="Next">
                                    <i class="fas fa-angle-right"></i>
                                </a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="{% url 'blog:blog-list' %}?page={{ blog.paginator.num_pages }}{% if query %}&q={{ query }}{% endif %}"
                                   aria-label="Last">
                                    <i class="fas fa-angle-double-right"></i>
                                </a>
                            </li>
                            {% endif %}
                        </ul>
                    </nav>
                    <div class="pagination-info text-center mt-3">
                        <small class="text-muted">
                            Showing {{ blog.start_index }} to {{ blog.end_index }} of {{ blog.paginator.count }} articles
                        </small>
                    </div>
                </div>
                {% endif %}
            </div>

            <!-- Enhanced Sidebar -->
            <div class="col-lg-4">
                <div class="blog-sidebar">
                    <!-- Quick Search Widget -->
                    <div class="sidebar-widget search-widget">
                        <h4 class="widget-title">
                            <i class="fas fa-search me-2"></i>Quick Search
                        </h4>
                        <form method="GET" class="sidebar-search-form">
                            <div class="search-input-group">
                                <input type="text" class="form-control" name="q" value="{{ query }}"
                                       placeholder="Search articles...">
                                <button type="submit" class="search-submit-btn">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </form>
                    </div>

                    <!-- Categories Widget -->
                    <div class="sidebar-widget categories-widget">
                        <h4 class="widget-title">
                            <i class="fas fa-folder me-2"></i>Categories
                        </h4>
                        <div class="categories-list">
                            {% for c in categories %}
                            <a href="{% url 'blog:category_detail' c.slug %}" class="category-item">
                                <span class="category-name">{{ c.title }}</span>
                                <span class="category-count">{{ c.post_set.count }}</span>
                            </a>
                            {% endfor %}
                        </div>
                    </div>

                    <!-- Recent Posts Widget -->
                    {% if featured_blog %}
                    <div class="sidebar-widget recent-posts-widget">
                        <h4 class="widget-title">
                            <i class="fas fa-clock me-2"></i>Recent Posts
                        </h4>
                        <div class="recent-posts-list">
                            {% for rp in featured_blog|slice:":4" %}
                            <div class="recent-post-item">
                                <div class="post-thumb">
                                    <a href="{% url 'blog:blog-detail' rp.pid %}">
                                        <img src="{{ rp.image.cdn_url }}" alt="{{ rp.title }}">
                                    </a>
                                </div>
                                <div class="post-info">
                                    <h6 class="post-title">
                                        <a href="{% url 'blog:blog-detail' rp.pid %}">{{ rp.title|truncatechars:50 }}</a>
                                    </h6>
                                    <div class="post-meta">
                                        <span class="post-date">{{ rp.date|date:"M d, Y" }}</span>
                                        <span class="post-views">{{ rp.views }} views</span>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                    {% endif %}

                    <!-- Newsletter Widget -->
                    <div class="sidebar-widget newsletter-widget">
                        <h4 class="widget-title">
                            <i class="fas fa-envelope me-2"></i>Stay Updated
                        </h4>
                        <p class="newsletter-text">Subscribe to our newsletter for the latest travel tips and destination guides.</p>
                        <form class="newsletter-form">
                            <div class="form-group mb-3">
                                <input type="email" class="form-control" placeholder="Your email address" required>
                            </div>
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="fas fa-paper-plane me-2"></i>Subscribe
                            </button>
                        </form>
                    </div>

                    <!-- Tags Widget -->
                    <div class="sidebar-widget tags-widget">
                        <h4 class="widget-title">
                            <i class="fas fa-tags me-2"></i>Popular Tags
                        </h4>
                        <div class="tags-cloud">
                            <a href="#" class="tag-item">Travel Tips</a>
                            <a href="#" class="tag-item">Adventure</a>
                            <a href="#" class="tag-item">Destinations</a>
                            <a href="#" class="tag-item">Culture</a>
                            <a href="#" class="tag-item">Photography</a>
                            <a href="#" class="tag-item">Food</a>
                            <a href="#" class="tag-item">Budget Travel</a>
                            <a href="#" class="tag-item">Luxury</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<style>
    /* Custom Font Declarations */
    @font-face {
        font-family: 'Fonarto';
        font-style: normal;
        font-weight: 400;
        src: local('Fonarto Regular'), url('{% static "fonts/FonartoRegular-8Mon2.woff" %}') format('woff');
        font-display: swap;
    }

    @font-face {
        font-family: 'Fonarto';
        font-style: normal;
        font-weight: 700;
        src: local('Fonarto Bold'), url('{% static "fonts/FonartoBold-RpYOo.woff" %}') format('woff');
        font-display: swap;
    }

    /* Hero Section Styles */
    .blog-hero-section {
        position: relative;
        overflow: hidden;
    }

    .hero-title {
        font-family: 'Fonarto', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        font-weight: 700;
        font-size: 3.5rem;
        line-height: 1.2;
        text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        margin-bottom: 1.5rem;
    }

    .hero-subtitle {
        font-size: 1.2rem;
        opacity: 0.9;
        line-height: 1.6;
    }

    .hero-stats {
        margin-top: 2rem;
    }

    .stat-item {
        text-align: center;
        padding: 0 1rem;
    }

    .stat-number {
        display: block;
        font-size: 2rem;
        font-weight: 700;
        color: #ff9d00;
    }

    .stat-label {
        display: block;
        font-size: 0.9rem;
        opacity: 0.8;
        text-transform: uppercase;
        letter-spacing: 1px;
    }

    /* Search and Filter Bar */
    .search-filter-bar {
        border: 1px solid #e9ecef;
        backdrop-filter: blur(10px);
        box-shadow: 0 8px 32px rgba(0,0,0,0.1);
    }

    .search-form .form-control:focus {
        border-color: #0f238d;
        box-shadow: 0 0 0 0.2rem rgba(15, 35, 141, 0.25);
    }

    /* Section Headings */
    .section-heading {
        font-family: 'Fonarto', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        font-weight: 700;
        color: #0f238d;
        font-size: 1.8rem;
        margin-bottom: 2rem;
    }

    /* Featured Posts */
    .featured-post-card {
        background: #fff;
        border-radius: 15px;
        overflow: hidden;
        box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
        height: 100%;
    }

    .featured-post-card:hover {
        transform: translateY(-8px);
        box-shadow: 0 15px 40px rgba(0,0,0,0.15);
    }

    .featured-post-card .post-image {
        position: relative;
        overflow: hidden;
        height: 200px;
    }

    .featured-post-card .post-image img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.3s ease;
    }

    .featured-post-card:hover .post-image img {
        transform: scale(1.05);
    }

    .post-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg, rgba(15, 35, 141, 0.8), rgba(255, 157, 0, 0.8));
        opacity: 0;
        transition: opacity 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .featured-post-card:hover .post-overlay {
        opacity: 1;
    }

    .featured-badge {
        position: absolute;
        top: 15px;
        left: 15px;
        background: #ff9d00;
        color: white;
        padding: 5px 12px;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .featured-post-card .post-content {
        padding: 1.5rem;
    }

    .featured-post-card .post-meta {
        display: flex;
        justify-content: space-between;
        margin-bottom: 1rem;
    }

    .category-tag {
        background: rgba(15, 35, 141, 0.1);
        color: #0f238d;
        padding: 4px 10px;
        border-radius: 12px;
        font-size: 0.8rem;
        font-weight: 600;
    }

    .read-time {
        color: #666;
        font-size: 0.8rem;
    }

    .featured-post-card .post-title a {
        color: #0f238d;
        text-decoration: none;
        font-weight: 600;
        line-height: 1.4;
        transition: color 0.3s ease;
    }

    .featured-post-card .post-title a:hover {
        color: #ff9d00;
    }

    /* Modern Blog Cards */
    .modern-blog-card {
        background: #fff;
        border-radius: 20px;
        overflow: hidden;
        box-shadow: 0 8px 32px rgba(0,0,0,0.08);
        transition: all 0.4s ease;
        height: 100%;
        display: flex;
        flex-direction: column;
    }

    .modern-blog-card:hover {
        transform: translateY(-10px);
        box-shadow: 0 20px 60px rgba(0,0,0,0.15);
    }

    .post-image-wrapper {
        position: relative;
        overflow: hidden;
        height: 250px;
    }

    .post-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.4s ease;
    }

    .modern-blog-card:hover .post-image {
        transform: scale(1.1);
    }

    .image-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg, rgba(15, 35, 141, 0.9), rgba(255, 157, 0, 0.9));
        opacity: 0;
        transition: opacity 0.4s ease;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .modern-blog-card:hover .image-overlay {
        opacity: 1;
    }

    .overlay-content i {
        color: white;
        font-size: 2rem;
        transform: translateX(-20px);
        transition: transform 0.4s ease;
    }

    .modern-blog-card:hover .overlay-content i {
        transform: translateX(0);
    }

    .category-badge {
        position: absolute;
        top: 15px;
        right: 15px;
        background: rgba(255, 255, 255, 0.95);
        color: #0f238d;
        padding: 6px 14px;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 600;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .modern-blog-card .post-content {
        padding: 2rem;
        flex-grow: 1;
        display: flex;
        flex-direction: column;
    }

    .modern-blog-card .post-meta {
        display: flex;
        flex-wrap: wrap;
        gap: 1rem;
        margin-bottom: 1rem;
        padding-bottom: 1rem;
        border-bottom: 1px solid #f0f0f0;
    }

    .meta-item {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        color: #666;
        font-size: 0.85rem;
    }

    .meta-item i {
        color: #ff9d00;
        font-size: 0.9rem;
    }

    .modern-blog-card .post-title {
        margin-bottom: 1rem;
        flex-grow: 1;
    }

    .modern-blog-card .post-title a {
        color: #0f238d;
        text-decoration: none;
        font-family: 'Fonarto', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        font-weight: 700;
        font-size: 1.3rem;
        line-height: 1.4;
        transition: color 0.3s ease;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }

    .modern-blog-card .post-title a:hover {
        color: #ff9d00;
    }

    .post-excerpt {
        color: #666;
        line-height: 1.6;
        margin-bottom: 1.5rem;
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }

    .post-footer {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: auto;
        padding-top: 1rem;
        border-top: 1px solid #f0f0f0;
    }

    .author-info {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        color: #666;
        font-size: 0.9rem;
    }

    .author-info i {
        color: #ff9d00;
    }

    .read-more-btn {
        color: #0f238d;
        text-decoration: none;
        font-weight: 600;
        font-size: 0.9rem;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .read-more-btn:hover {
        color: #ff9d00;
        transform: translateX(5px);
    }

    .read-more-btn i {
        transition: transform 0.3s ease;
    }

    .read-more-btn:hover i {
        transform: translateX(3px);
    }

    /* Enhanced Pagination */
    .modern-pagination .pagination {
        gap: 0.5rem;
    }

    .modern-pagination .page-link {
        border: 2px solid #e9ecef;
        color: #0f238d;
        padding: 0.75rem 1rem;
        border-radius: 10px;
        font-weight: 600;
        transition: all 0.3s ease;
        text-decoration: none;
    }

    .modern-pagination .page-link:hover {
        background: #0f238d;
        border-color: #0f238d;
        color: white;
        transform: translateY(-2px);
    }

    .modern-pagination .page-item.active .page-link {
        background: #ff9d00;
        border-color: #ff9d00;
        color: white;
    }

    .pagination-info {
        margin-top: 1rem;
    }

    /* Enhanced Sidebar */
    .blog-sidebar {
        position: sticky;
        top: 2rem;
    }

    .sidebar-widget {
        background: #fff;
        border-radius: 20px;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 8px 32px rgba(0,0,0,0.08);
        border: 1px solid #f0f0f0;
        transition: all 0.3s ease;
    }

    .sidebar-widget:hover {
        box-shadow: 0 15px 40px rgba(0,0,0,0.12);
        transform: translateY(-2px);
    }

    .sidebar-widget .widget-title {
        font-family: 'Fonarto', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        font-weight: 700;
        color: #0f238d;
        font-size: 1.3rem;
        margin-bottom: 1.5rem;
        padding-bottom: 1rem;
        border-bottom: 2px solid #f0f0f0;
        position: relative;
    }

    .sidebar-widget .widget-title::after {
        content: '';
        position: absolute;
        bottom: -2px;
        left: 0;
        width: 50px;
        height: 2px;
        background: #ff9d00;
    }

    /* Search Widget */
    .search-input-group {
        position: relative;
        display: flex;
    }

    .search-input-group .form-control {
        border: 2px solid #e9ecef;
        border-radius: 25px 0 0 25px;
        padding: 0.75rem 1rem;
        font-size: 0.95rem;
    }

    .search-input-group .form-control:focus {
        border-color: #0f238d;
        box-shadow: none;
    }

    .search-submit-btn {
        background: #0f238d;
        border: 2px solid #0f238d;
        color: white;
        padding: 0.75rem 1.5rem;
        border-radius: 0 25px 25px 0;
        transition: all 0.3s ease;
    }

    .search-submit-btn:hover {
        background: #ff9d00;
        border-color: #ff9d00;
    }

    /* Categories Widget */
    .categories-list {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
    }

    .category-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.75rem 1rem;
        background: #f8f9fa;
        border-radius: 10px;
        text-decoration: none;
        color: #333;
        transition: all 0.3s ease;
        border: 1px solid transparent;
    }

    .category-item:hover {
        background: #0f238d;
        color: white;
        transform: translateX(5px);
    }

    .category-count {
        background: rgba(255, 157, 0, 0.2);
        color: #ff9d00;
        padding: 0.25rem 0.5rem;
        border-radius: 12px;
        font-size: 0.8rem;
        font-weight: 600;
        min-width: 30px;
        text-align: center;
    }

    .category-item:hover .category-count {
        background: rgba(255, 255, 255, 0.2);
        color: white;
    }

    /* Recent Posts Widget */
    .recent-posts-list {
        display: flex;
        flex-direction: column;
        gap: 1rem;
    }

    .recent-post-item {
        display: flex;
        gap: 1rem;
        padding: 1rem;
        background: #f8f9fa;
        border-radius: 12px;
        transition: all 0.3s ease;
    }

    .recent-post-item:hover {
        background: #e9ecef;
        transform: translateX(5px);
    }

    .recent-post-item .post-thumb {
        flex-shrink: 0;
        width: 60px;
        height: 60px;
        border-radius: 8px;
        overflow: hidden;
    }

    .recent-post-item .post-thumb img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .recent-post-item .post-info {
        flex-grow: 1;
    }

    .recent-post-item .post-title {
        margin-bottom: 0.5rem;
    }

    .recent-post-item .post-title a {
        color: #0f238d;
        text-decoration: none;
        font-weight: 600;
        font-size: 0.9rem;
        line-height: 1.3;
        transition: color 0.3s ease;
    }

    .recent-post-item .post-title a:hover {
        color: #ff9d00;
    }

    .recent-post-item .post-meta {
        display: flex;
        gap: 1rem;
        font-size: 0.8rem;
        color: #666;
    }

    /* Newsletter Widget */
    .newsletter-widget {
        background: linear-gradient(135deg, #0f238d 0%, #ff9d00 100%);
        color: white;
    }

    .newsletter-widget .widget-title {
        color: white;
        border-bottom-color: rgba(255, 255, 255, 0.2);
    }

    .newsletter-widget .widget-title::after {
        background: white;
    }

    .newsletter-text {
        opacity: 0.9;
        line-height: 1.6;
        margin-bottom: 1.5rem;
    }

    .newsletter-form .form-control {
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.3);
        color: white;
        backdrop-filter: blur(10px);
    }

    .newsletter-form .form-control::placeholder {
        color: rgba(255, 255, 255, 0.7);
    }

    .newsletter-form .form-control:focus {
        background: rgba(255, 255, 255, 0.2);
        border-color: white;
        box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.25);
        color: white;
    }

    .newsletter-form .btn-primary {
        background: white;
        border-color: white;
        color: #0f238d;
        font-weight: 600;
    }

    .newsletter-form .btn-primary:hover {
        background: #f8f9fa;
        border-color: #f8f9fa;
        transform: translateY(-2px);
    }

    /* Tags Widget */
    .tags-cloud {
        display: flex;
        flex-wrap: wrap;
        gap: 0.5rem;
    }

    .tag-item {
        background: #f8f9fa;
        color: #0f238d;
        padding: 0.5rem 1rem;
        border-radius: 20px;
        text-decoration: none;
        font-size: 0.85rem;
        font-weight: 500;
        transition: all 0.3s ease;
        border: 1px solid #e9ecef;
    }

    .tag-item:hover {
        background: #0f238d;
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(15, 35, 141, 0.3);
    }

    /* No Posts Found */
    .no-posts-found {
        background: #f8f9fa;
        border-radius: 15px;
        padding: 3rem 2rem;
    }

    /* View Toggle */
    .view-toggle-btn {
        background: #f8f9fa;
        border: 1px solid #e9ecef;
        color: #0f238d;
        transition: all 0.3s ease;
    }

    .view-toggle-btn:hover {
        background: #0f238d;
        border-color: #0f238d;
        color: white;
    }

    /* Responsive Design */
    @media (max-width: 1199px) {
        .hero-title {
            font-size: 3rem;
        }

        .modern-blog-card .post-content {
            padding: 1.5rem;
        }
    }

    @media (max-width: 991px) {
        .hero-title {
            font-size: 2.5rem;
        }

        .hero-stats {
            margin-top: 1.5rem;
        }

        .stat-item {
            padding: 0 0.5rem;
        }

        .blog-sidebar {
            position: static;
            margin-top: 3rem;
        }

        .search-filter-bar {
            text-align: center;
        }

        .filter-options {
            justify-content: center !important;
            margin-top: 1rem;
        }
    }

    @media (max-width: 767px) {
        .hero-title {
            font-size: 2rem;
        }

        .hero-subtitle {
            font-size: 1rem;
        }

        .stat-number {
            font-size: 1.5rem;
        }

        .stat-label {
            font-size: 0.8rem;
        }

        .modern-blog-card .post-content {
            padding: 1.25rem;
        }

        .modern-blog-card .post-title a {
            font-size: 1.1rem;
        }

        .post-image-wrapper {
            height: 200px;
        }

        .featured-post-card .post-image {
            height: 180px;
        }

        .sidebar-widget {
            padding: 1.5rem;
        }

        .recent-post-item {
            flex-direction: column;
            text-align: center;
        }

        .recent-post-item .post-thumb {
            align-self: center;
            width: 80px;
            height: 80px;
        }
    }

    @media (max-width: 575px) {
        .hero-stats {
            flex-direction: column;
            gap: 1rem;
        }

        .search-filter-bar .row {
            flex-direction: column;
        }

        .filter-options {
            margin-top: 1rem;
        }

        .modern-pagination .page-link {
            padding: 0.5rem 0.75rem;
            font-size: 0.9rem;
        }
    }

    /* Animation Classes */
    .fade-in {
        animation: fadeIn 0.6s ease-in-out;
    }

    @keyframes fadeIn {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* Loading States */
    .loading {
        opacity: 0.6;
        pointer-events: none;
    }

    .loading::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 20px;
        height: 20px;
        margin: -10px 0 0 -10px;
        border: 2px solid #f3f3f3;
        border-top: 2px solid #0f238d;
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
</style>

<!-- JavaScript for Enhanced Functionality -->
<script>
    // View Toggle Functionality
    function toggleView() {
        const container = document.getElementById('posts-container');
        const viewText = document.getElementById('view-text');
        const isGrid = container.classList.contains('row');

        if (isGrid) {
            // Switch to list view
            container.classList.remove('row');
            container.classList.add('list-view');
            viewText.textContent = 'List';

            // Update card classes for list view
            const cards = container.querySelectorAll('.blog-post-item');
            cards.forEach(card => {
                card.classList.remove('col-lg-6', 'col-md-6');
                card.classList.add('col-12');
            });
        } else {
            // Switch to grid view
            container.classList.remove('list-view');
            container.classList.add('row');
            viewText.textContent = 'Grid';

            // Update card classes for grid view
            const cards = container.querySelectorAll('.blog-post-item');
            cards.forEach(card => {
                card.classList.remove('col-12');
                card.classList.add('col-lg-6', 'col-md-6');
            });
        }
    }

    // Smooth scroll for pagination
    document.addEventListener('DOMContentLoaded', function() {
        const paginationLinks = document.querySelectorAll('.modern-pagination .page-link');
        paginationLinks.forEach(link => {
            link.addEventListener('click', function(e) {
                // Add loading state
                document.getElementById('blog-posts-grid').classList.add('loading');

                // Scroll to top of blog section
                document.querySelector('.blog-content-section').scrollIntoView({
                    behavior: 'smooth'
                });
            });
        });

        // Add fade-in animation to blog cards
        const blogCards = document.querySelectorAll('.modern-blog-card');
        blogCards.forEach((card, index) => {
            card.style.animationDelay = `${index * 0.1}s`;
            card.classList.add('fade-in');
        });
    });

    // Search form enhancement
    document.addEventListener('DOMContentLoaded', function() {
        const searchForms = document.querySelectorAll('.search-form, .sidebar-search-form');
        searchForms.forEach(form => {
            form.addEventListener('submit', function() {
                const submitBtn = form.querySelector('button[type="submit"]');
                const originalContent = submitBtn.innerHTML;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
                submitBtn.disabled = true;

                // Re-enable after a short delay (form will submit)
                setTimeout(() => {
                    submitBtn.innerHTML = originalContent;
                    submitBtn.disabled = false;
                }, 2000);
            });
        });
    });
</script>

{% include 'users/footer.html' %}
{% endblock %}