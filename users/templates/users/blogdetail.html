{% extends 'users/basemain.html' %}
{% load static %}

{% block content %}
<!-- Breadcrumb Navigation -->
<section class="breadcrumb-section py-3" style="background: #f8f9fa;">
    <div class="container">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb mb-0">
                <li class="breadcrumb-item">
                    <a href="{% url 'users:users-home' %}" class="text-decoration-none">
                        <i class="fas fa-home me-1"></i>Home
                    </a>
                </li>
                <li class="breadcrumb-item">
                    <a href="{% url 'blog:blog-list' %}" class="text-decoration-none">Blog</a>
                </li>
                {% if post.category %}
                <li class="breadcrumb-item">
                    <a href="{% url 'blog:category_detail' post.category.slug %}" class="text-decoration-none">
                        {{ post.category.title }}
                    </a>
                </li>
                {% endif %}
                <li class="breadcrumb-item active" aria-current="page">{{ post.title|truncatechars:50 }}</li>
            </ol>
        </nav>
    </div>
</section>

<!-- Hero Banner Section -->
<section class="blog-hero-banner" style="
    background: linear-gradient(135deg, rgba(15, 35, 141, 0.8) 0%, rgba(255, 157, 0, 0.8) 100%),
                url('{{ post.image.cdn_url }}');
    background-size: cover;
    background-position: center;
    background-attachment: fixed;
    padding: 120px 0 80px;
    position: relative;
">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="hero-content text-center text-white">
                    <!-- Back to Blog Button -->
                    <div class="back-to-blog mb-4">
                        <a href="{% url 'blog:blog-list' %}" class="btn btn-outline-light btn-sm">
                            <i class="fas fa-arrow-left me-2"></i>Back to Blog
                        </a>
                    </div>

                    <!-- Post Meta -->
                    <div class="post-meta-hero mb-4">
                        <div class="meta-items d-flex justify-content-center flex-wrap gap-3">
                            <div class="meta-item">
                                <i class="far fa-calendar-alt me-2"></i>
                                <span>{{ post.date|date:"F d, Y" }}</span>
                            </div>
                            {% if post.category %}
                            <div class="meta-item">
                                <i class="far fa-folder me-2"></i>
                                <span>{{ post.category.title }}</span>
                            </div>
                            {% endif %}
                            <div class="meta-item">
                                <i class="far fa-user me-2"></i>
                                <span>{{ post.user.first_name|default:post.user.username }}</span>
                            </div>
                            <div class="meta-item">
                                <i class="far fa-clock me-2"></i>
                                <span>{{ post.get_read_time }} min read</span>
                            </div>
                            <div class="meta-item">
                                <i class="far fa-eye me-2"></i>
                                <span>{{ post.views }} views</span>
                            </div>
                        </div>
                    </div>

                    <!-- Post Title -->
                    <h1 class="hero-title mb-4">{{ post.title }}</h1>

                    <!-- Quick Share -->
                    <div class="quick-share">
                        <span class="share-label me-3">Share:</span>
                        <div class="share-buttons d-inline-flex gap-2">
                            <a href="https://www.facebook.com/sharer/sharer.php?u={{ request.build_absolute_uri }}"
                               class="share-btn facebook" target="_blank" title="Share on Facebook">
                                <i class="fab fa-facebook-f"></i>
                            </a>
                            <a href="https://twitter.com/intent/tweet?url={{ request.build_absolute_uri }}&text={{ post.title }}"
                               class="share-btn twitter" target="_blank" title="Share on Twitter">
                                <i class="fab fa-twitter"></i>
                            </a>
                            <a href="https://wa.me/?text={{ post.title }} - {{ request.build_absolute_uri }}"
                               class="share-btn whatsapp" target="_blank" title="Share on WhatsApp">
                                <i class="fab fa-whatsapp"></i>
                            </a>
                            <a href="mailto:?subject={{ post.title }}&body=Check out this article: {{ request.build_absolute_uri }}"
                               class="share-btn email" title="Share via Email">
                                <i class="fas fa-envelope"></i>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Messages Section -->
{% if messages %}
<div class="container mt-4">
    {% for message in messages %}
    <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
        {{ message }}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
    {% endfor %}
</div>
{% endif %}

<!-- Blog Content Section -->
<section class="blog-content-section py-5" style="background: linear-gradient(to bottom, #ffffff 0%, #f8f9fa 100%);">
    <div class="container">
        <div class="row">
            <!-- Main Content -->
            <div class="col-lg-8">
                <article class="blog-article">
                    <!-- Article Header -->
                    <div class="article-header mb-5">
                        <div class="article-meta d-flex flex-wrap gap-3 mb-3">
                            <div class="meta-badge">
                                <i class="fas fa-tag me-1"></i>
                                <span>{{ post.category.title|default:"Uncategorized" }}</span>
                            </div>
                            <div class="meta-badge">
                                <i class="fas fa-clock me-1"></i>
                                <span>{{ post.get_read_time }} min read</span>
                            </div>
                            <div class="meta-badge">
                                <i class="fas fa-eye me-1"></i>
                                <span>{{ post.views }} views</span>
                            </div>
                        </div>
                    </div>

                    <!-- Article Content -->
                    <div class="article-content">
                        <div class="content-wrapper">
                            <div class="post-text">
                                {{ post.content|linebreaks }}
                            </div>
                        </div>

                        <!-- Article Footer -->
                        <div class="article-footer mt-5">
                            <!-- Tags Section -->
                            {% if post.tags.all %}
                            <div class="article-tags mb-4">
                                <h6 class="tags-title mb-3">
                                    <i class="fas fa-tags me-2"></i>Tags
                                </h6>
                                <div class="tags-list">
                                    {% for tag in post.tags.all %}
                                    <span class="tag-item">{{ tag.name }}</span>
                                    {% endfor %}
                                </div>
                            </div>
                            {% endif %}

                            <!-- Enhanced Share Section -->
                            <div class="share-section">
                                <h6 class="share-title mb-3">
                                    <i class="fas fa-share-alt me-2"></i>Share this article
                                </h6>
                                <div class="share-buttons-grid">
                                    <a href="https://www.facebook.com/sharer/sharer.php?u={{ request.build_absolute_uri }}"
                                       class="share-button facebook" target="_blank">
                                        <i class="fab fa-facebook-f"></i>
                                        <span>Facebook</span>
                                    </a>
                                    <a href="https://twitter.com/intent/tweet?url={{ request.build_absolute_uri }}&text={{ post.title }}"
                                       class="share-button twitter" target="_blank">
                                        <i class="fab fa-twitter"></i>
                                        <span>Twitter</span>
                                    </a>
                                    <a href="https://www.linkedin.com/sharing/share-offsite/?url={{ request.build_absolute_uri }}"
                                       class="share-button linkedin" target="_blank">
                                        <i class="fab fa-linkedin-in"></i>
                                        <span>LinkedIn</span>
                                    </a>
                                    <a href="https://wa.me/?text={{ post.title }} - {{ request.build_absolute_uri }}"
                                       class="share-button whatsapp" target="_blank">
                                        <i class="fab fa-whatsapp"></i>
                                        <span>WhatsApp</span>
                                    </a>
                                    <a href="mailto:?subject={{ post.title }}&body=Check out this article: {{ request.build_absolute_uri }}"
                                       class="share-button email">
                                        <i class="fas fa-envelope"></i>
                                        <span>Email</span>
                                    </a>
                                    <button class="share-button copy-link" onclick="copyToClipboard('{{ request.build_absolute_uri }}')">
                                        <i class="fas fa-link"></i>
                                        <span>Copy Link</span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Enhanced Comments Section -->
                    <div class="comments-section mt-5">
                        <div class="comments-header mb-4">
                            <h3 class="comments-title">
                                <i class="fas fa-comments me-2"></i>
                                Comments <span class="comment-count">({{ comment.count }})</span>
                            </h3>
                        </div>

                        {% if comment %}
                        <div class="comments-list mb-5">
                            {% for c in comment %}
                            <div class="comment-item">
                                <div class="comment-avatar">
                                    <div class="avatar-circle">
                                        {{ c.full_name|first|upper }}
                                    </div>
                                </div>
                                <div class="comment-content">
                                    <div class="comment-header">
                                        <h6 class="comment-author">{{ c.full_name|title }}</h6>
                                        <span class="comment-date">
                                            <i class="far fa-clock me-1"></i>
                                            {{ c.date|date:"M d, Y \a\t H:i" }}
                                        </span>
                                    </div>
                                    <div class="comment-text">
                                        <p>{{ c.comment }}</p>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                        {% else %}
                        <div class="no-comments">
                            <div class="no-comments-icon">
                                <i class="far fa-comments"></i>
                            </div>
                            <h5>No Comments Yet</h5>
                            <p>Be the first to share your thoughts on this article!</p>
                        </div>
                        {% endif %}

                        <!-- Enhanced Comment Form -->
                        <div class="comment-form-section">
                            <h4 class="form-title mb-4">
                                <i class="fas fa-edit me-2"></i>Join the Discussion
                            </h4>
                            <form class="modern-comment-form" method="POST" action="{% url 'blog:blog-detail' post.pid %}">
                                {% csrf_token %}
                                <div class="form-grid">
                                    <div class="form-group">
                                        <label for="fullName" class="form-label">Full Name *</label>
                                        <input type="text" class="form-control" id="fullName" name="full_name"
                                               placeholder="Enter your full name" required>
                                    </div>
                                    <div class="form-group">
                                        <label for="email" class="form-label">Email Address *</label>
                                        <input type="email" class="form-control" id="email" name="email"
                                               placeholder="Enter your email" required>
                                    </div>
                                    <div class="form-group full-width">
                                        <label for="comment" class="form-label">Your Comment *</label>
                                        <textarea class="form-control" id="comment" name="comment" rows="5"
                                                  placeholder="Share your thoughts..." required></textarea>
                                    </div>
                                    <div class="form-group full-width">
                                        <button type="submit" class="submit-btn">
                                            <i class="fas fa-paper-plane me-2"></i>
                                            Post Comment
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Post Navigation -->
                    {% if previous_post or next_post %}
                    <div class="post-navigation mt-5">
                        <div class="nav-container">
                            {% if previous_post %}
                            <div class="nav-item prev-post">
                                <a href="{% url 'blog:blog-detail' previous_post.pid %}" class="nav-link">
                                    <div class="nav-direction">
                                        <i class="fas fa-arrow-left me-2"></i>Previous Article
                                    </div>
                                    <div class="nav-title">{{ previous_post.title|truncatechars:50 }}</div>
                                </a>
                            </div>
                            {% endif %}

                            {% if next_post %}
                            <div class="nav-item next-post">
                                <a href="{% url 'blog:blog-detail' next_post.pid %}" class="nav-link">
                                    <div class="nav-direction">
                                        Next Article<i class="fas fa-arrow-right ms-2"></i>
                                    </div>
                                    <div class="nav-title">{{ next_post.title|truncatechars:50 }}</div>
                                </a>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                    {% endif %}
                </article>
            </div>

            <!-- Sidebar -->
            <div class="col-lg-4">
                <div class="blog-sidebar">
                    <!-- Author Bio -->
                    <div class="sidebar-widget author-widget">
                        <h5 class="widget-title">
                            <i class="fas fa-user me-2"></i>About the Author
                        </h5>
                        <div class="author-info">
                            <div class="author-avatar">
                                <div class="avatar-circle large">
                                    {{ post.user.first_name|first|default:post.user.username|first|upper }}
                                </div>
                            </div>
                            <div class="author-details">
                                <h6 class="author-name">{{ post.user.first_name|default:post.user.username }}</h6>
                                <p class="author-bio">Travel enthusiast and content creator sharing amazing destinations and travel tips.</p>
                                <div class="author-stats">
                                    <div class="stat-item">
                                        <span class="stat-number">{{ post.user.post_set.count }}</span>
                                        <span class="stat-label">Articles</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Related Posts -->
                    {% if related_blogs %}
                    <div class="sidebar-widget related-posts-widget">
                        <h5 class="widget-title">
                            <i class="fas fa-bookmark me-2"></i>Related Articles
                        </h5>
                        <div class="related-posts-list">
                            {% for rp in related_blogs|slice:":4" %}
                            <div class="related-post-item">
                                <div class="post-thumb">
                                    <a href="{% url 'blog:blog-detail' rp.pid %}">
                                        <img src="{{ rp.image.cdn_url }}" alt="{{ rp.title }}">
                                    </a>
                                </div>
                                <div class="post-info">
                                    <h6 class="post-title">
                                        <a href="{% url 'blog:blog-detail' rp.pid %}">{{ rp.title|truncatechars:60 }}</a>
                                    </h6>
                                    <div class="post-meta">
                                        <span class="post-date">{{ rp.date|date:"M d, Y" }}</span>
                                        <span class="read-time">{{ rp.get_read_time }} min</span>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                    {% endif %}

                    <!-- Navigation Widget -->
                    <div class="sidebar-widget navigation-widget">
                        <h5 class="widget-title">
                            <i class="fas fa-compass me-2"></i>Quick Navigation
                        </h5>
                        <div class="nav-buttons">
                            <a href="{% url 'blog:blog-list' %}" class="nav-btn">
                                <i class="fas fa-list me-2"></i>All Articles
                            </a>
                            {% if post.category %}
                            <a href="{% url 'blog:category_detail' post.category.slug %}" class="nav-btn">
                                <i class="fas fa-folder me-2"></i>{{ post.category.title }}
                            </a>
                            {% endif %}
                            <a href="{% url 'users:users-home' %}" class="nav-btn">
                                <i class="fas fa-home me-2"></i>Back to Home
                            </a>
                        </div>
                    </div>

                    <!-- Newsletter Signup -->
                    <div class="sidebar-widget newsletter-widget">
                        <h5 class="widget-title">
                            <i class="fas fa-envelope me-2"></i>Stay Updated
                        </h5>
                        <p class="newsletter-text">Get the latest travel tips and destination guides delivered to your inbox.</p>
                        <form class="newsletter-form">
                            <div class="form-group mb-3">
                                <input type="email" class="form-control" placeholder="Your email address" required>
                            </div>
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="fas fa-paper-plane me-2"></i>Subscribe
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<style>
    /* Custom Font Declarations */
    @font-face {
        font-family: 'Fonarto';
        font-style: normal;
        font-weight: 400;
        src: local('Fonarto Regular'), url('{% static "fonts/FonartoRegular-8Mon2.woff" %}') format('woff');
        font-display: swap;
    }

    @font-face {
        font-family: 'Fonarto';
        font-style: normal;
        font-weight: 700;
        src: local('Fonarto Bold'), url('{% static "fonts/FonartoBold-RpYOo.woff" %}') format('woff');
        font-display: swap;
    }

    /* Breadcrumb Styles */
    .breadcrumb-section {
        border-bottom: 1px solid #e9ecef;
    }

    .breadcrumb {
        background: none;
        padding: 0;
        margin: 0;
    }

    .breadcrumb-item a {
        color: #0f238d;
        transition: color 0.3s ease;
    }

    .breadcrumb-item a:hover {
        color: #ff9d00;
    }

    .breadcrumb-item.active {
        color: #666;
    }

    /* Hero Banner Styles */
    .blog-hero-banner {
        position: relative;
        overflow: hidden;
    }

    .hero-title {
        font-family: 'Fonarto', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        font-weight: 700;
        font-size: 3rem;
        line-height: 1.2;
        text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        margin-bottom: 2rem;
    }

    .back-to-blog .btn {
        backdrop-filter: blur(10px);
        border: 2px solid rgba(255, 255, 255, 0.3);
        transition: all 0.3s ease;
    }

    .back-to-blog .btn:hover {
        background: rgba(255, 255, 255, 0.2);
        border-color: rgba(255, 255, 255, 0.5);
        transform: translateY(-2px);
    }

    .post-meta-hero .meta-items {
        background: rgba(255, 255, 255, 0.1);
        padding: 1rem 2rem;
        border-radius: 50px;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .meta-item {
        font-size: 0.9rem;
        opacity: 0.9;
        display: flex;
        align-items: center;
    }

    .quick-share {
        margin-top: 2rem;
    }

    .share-label {
        font-weight: 600;
        opacity: 0.9;
    }

    .share-btn {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        color: white;
        text-decoration: none;
        transition: all 0.3s ease;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.3);
    }

    .share-btn:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.3);
        color: white;
    }

    .share-btn.facebook { background: rgba(59, 89, 152, 0.8); }
    .share-btn.twitter { background: rgba(29, 161, 242, 0.8); }
    .share-btn.whatsapp { background: rgba(37, 211, 102, 0.8); }
    .share-btn.email { background: rgba(108, 117, 125, 0.8); }

    /* Main Article Styles */
    .blog-article {
        background: #fff;
        border-radius: 20px;
        padding: 3rem;
        box-shadow: 0 8px 32px rgba(0,0,0,0.08);
        margin-bottom: 2rem;
    }

    .article-meta {
        padding-bottom: 1.5rem;
        border-bottom: 2px solid #f0f0f0;
    }

    .meta-badge {
        background: rgba(15, 35, 141, 0.1);
        color: #0f238d;
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-size: 0.85rem;
        font-weight: 600;
        display: flex;
        align-items: center;
    }

    .meta-badge i {
        color: #ff9d00;
    }

    /* Article Content */
    .article-content {
        margin-top: 2rem;
    }

    .post-text {
        font-size: 1.1rem;
        line-height: 1.8;
        color: #333;
    }

    .post-text p {
        margin-bottom: 1.5rem;
    }

    .post-text h1, .post-text h2, .post-text h3, .post-text h4, .post-text h5, .post-text h6 {
        font-family: 'Fonarto', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        font-weight: 700;
        color: #0f238d;
        margin-top: 2rem;
        margin-bottom: 1rem;
    }

    .post-text img {
        max-width: 100%;
        height: auto;
        border-radius: 10px;
        margin: 1.5rem 0;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }

    /* Article Footer */
    .article-footer {
        border-top: 2px solid #f0f0f0;
        padding-top: 2rem;
    }

    .tags-title, .share-title {
        font-family: 'Fonarto', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        font-weight: 700;
        color: #0f238d;
        font-size: 1.1rem;
    }

    .tags-list {
        display: flex;
        flex-wrap: wrap;
        gap: 0.5rem;
    }

    .tag-item {
        background: #f8f9fa;
        color: #0f238d;
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-size: 0.85rem;
        font-weight: 500;
        border: 1px solid #e9ecef;
        transition: all 0.3s ease;
    }

    .tag-item:hover {
        background: #0f238d;
        color: white;
        transform: translateY(-2px);
    }

    /* Enhanced Share Section */
    .share-section {
        margin-top: 2rem;
        padding: 2rem;
        background: #f8f9fa;
        border-radius: 15px;
        border: 1px solid #e9ecef;
    }

    .share-buttons-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 1rem;
    }

    .share-button {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
        padding: 0.75rem 1rem;
        border-radius: 10px;
        text-decoration: none;
        font-weight: 600;
        font-size: 0.9rem;
        transition: all 0.3s ease;
        border: none;
        cursor: pointer;
    }

    .share-button.facebook {
        background: #1877f2;
        color: white;
    }

    .share-button.twitter {
        background: #1da1f2;
        color: white;
    }

    .share-button.linkedin {
        background: #0077b5;
        color: white;
    }

    .share-button.whatsapp {
        background: #25d366;
        color: white;
    }

    .share-button.email {
        background: #6c757d;
        color: white;
    }

    .share-button.copy-link {
        background: #0f238d;
        color: white;
    }

    .share-button:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        color: white;
    }

    /* Comments Section */
    .comments-section {
        margin-top: 3rem;
        padding-top: 2rem;
        border-top: 2px solid #f0f0f0;
    }

    .comments-title {
        font-family: 'Fonarto', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        font-weight: 700;
        color: #0f238d;
        font-size: 1.8rem;
    }

    .comment-count {
        color: #ff9d00;
        font-size: 1rem;
    }

    .comment-item {
        display: flex;
        gap: 1rem;
        margin-bottom: 2rem;
        padding: 1.5rem;
        background: #f8f9fa;
        border-radius: 15px;
        border: 1px solid #e9ecef;
        transition: all 0.3s ease;
    }

    .comment-item:hover {
        background: #e9ecef;
        transform: translateX(5px);
    }

    .comment-avatar .avatar-circle {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        background: linear-gradient(135deg, #0f238d, #ff9d00);
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 700;
        font-size: 1.2rem;
    }

    .comment-content {
        flex-grow: 1;
    }

    .comment-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 0.5rem;
    }

    .comment-author {
        font-weight: 700;
        color: #0f238d;
        margin: 0;
    }

    .comment-date {
        font-size: 0.85rem;
        color: #666;
    }

    .comment-text p {
        margin: 0;
        line-height: 1.6;
        color: #333;
    }

    /* No Comments State */
    .no-comments {
        text-align: center;
        padding: 3rem 2rem;
        background: #f8f9fa;
        border-radius: 15px;
        border: 2px dashed #dee2e6;
    }

    .no-comments-icon i {
        font-size: 3rem;
        color: #dee2e6;
        margin-bottom: 1rem;
    }

    .no-comments h5 {
        color: #666;
        margin-bottom: 0.5rem;
    }

    .no-comments p {
        color: #999;
        margin: 0;
    }

    /* Modern Comment Form */
    .comment-form-section {
        margin-top: 3rem;
        padding: 2rem;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-radius: 20px;
        border: 1px solid #dee2e6;
    }

    .form-title {
        font-family: 'Fonarto', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        font-weight: 700;
        color: #0f238d;
        font-size: 1.5rem;
        margin-bottom: 1.5rem;
    }

    .form-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 1.5rem;
    }

    .form-group.full-width {
        grid-column: 1 / -1;
    }

    .form-label {
        font-weight: 600;
        color: #0f238d;
        margin-bottom: 0.5rem;
        display: block;
    }

    .modern-comment-form .form-control {
        border: 2px solid #dee2e6;
        border-radius: 10px;
        padding: 0.75rem 1rem;
        font-size: 0.95rem;
        transition: all 0.3s ease;
        background: white;
    }

    .modern-comment-form .form-control:focus {
        border-color: #0f238d;
        box-shadow: 0 0 0 0.2rem rgba(15, 35, 141, 0.25);
        outline: none;
    }

    .modern-comment-form textarea.form-control {
        resize: vertical;
        min-height: 120px;
    }

    .submit-btn {
        background: linear-gradient(135deg, #0f238d 0%, #ff9d00 100%);
        color: white;
        border: none;
        padding: 1rem 2rem;
        border-radius: 50px;
        font-weight: 600;
        font-size: 1rem;
        transition: all 0.3s ease;
        cursor: pointer;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .submit-btn:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(15, 35, 141, 0.3);
    }

    /* Sidebar Styles */
    .blog-sidebar {
        position: sticky;
        top: 2rem;
    }

    .sidebar-widget {
        background: #fff;
        border-radius: 20px;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 8px 32px rgba(0,0,0,0.08);
        border: 1px solid #f0f0f0;
        transition: all 0.3s ease;
    }

    .sidebar-widget:hover {
        box-shadow: 0 15px 40px rgba(0,0,0,0.12);
        transform: translateY(-2px);
    }

    .sidebar-widget .widget-title {
        font-family: 'Fonarto', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        font-weight: 700;
        color: #0f238d;
        font-size: 1.3rem;
        margin-bottom: 1.5rem;
        padding-bottom: 1rem;
        border-bottom: 2px solid #f0f0f0;
        position: relative;
    }

    .sidebar-widget .widget-title::after {
        content: '';
        position: absolute;
        bottom: -2px;
        left: 0;
        width: 50px;
        height: 2px;
        background: #ff9d00;
    }

    /* Author Widget */
    .author-info {
        text-align: center;
    }

    .author-avatar .avatar-circle.large {
        width: 80px;
        height: 80px;
        font-size: 2rem;
        margin: 0 auto 1rem;
    }

    .author-name {
        font-weight: 700;
        color: #0f238d;
        margin-bottom: 0.5rem;
    }

    .author-bio {
        color: #666;
        line-height: 1.6;
        margin-bottom: 1rem;
    }

    .author-stats {
        display: flex;
        justify-content: center;
        gap: 2rem;
    }

    .stat-item {
        text-align: center;
    }

    .stat-number {
        display: block;
        font-size: 1.5rem;
        font-weight: 700;
        color: #ff9d00;
    }

    .stat-label {
        font-size: 0.85rem;
        color: #666;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    /* Related Posts Widget */
    .related-posts-list {
        display: flex;
        flex-direction: column;
        gap: 1rem;
    }

    .related-post-item {
        display: flex;
        gap: 1rem;
        padding: 1rem;
        background: #f8f9fa;
        border-radius: 12px;
        transition: all 0.3s ease;
        border: 1px solid #e9ecef;
    }

    .related-post-item:hover {
        background: #e9ecef;
        transform: translateX(5px);
    }

    .related-post-item .post-thumb {
        flex-shrink: 0;
        width: 70px;
        height: 70px;
        border-radius: 8px;
        overflow: hidden;
    }

    .related-post-item .post-thumb img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.3s ease;
    }

    .related-post-item:hover .post-thumb img {
        transform: scale(1.1);
    }

    .related-post-item .post-info {
        flex-grow: 1;
    }

    .related-post-item .post-title {
        margin-bottom: 0.5rem;
    }

    .related-post-item .post-title a {
        color: #0f238d;
        text-decoration: none;
        font-weight: 600;
        font-size: 0.95rem;
        line-height: 1.3;
        transition: color 0.3s ease;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }

    .related-post-item .post-title a:hover {
        color: #ff9d00;
    }

    .related-post-item .post-meta {
        display: flex;
        gap: 1rem;
        font-size: 0.8rem;
        color: #666;
    }

    /* Navigation Widget */
    .nav-buttons {
        display: flex;
        flex-direction: column;
        gap: 0.75rem;
    }

    .nav-btn {
        display: flex;
        align-items: center;
        padding: 0.75rem 1rem;
        background: #f8f9fa;
        color: #0f238d;
        text-decoration: none;
        border-radius: 10px;
        font-weight: 600;
        transition: all 0.3s ease;
        border: 1px solid #e9ecef;
    }

    .nav-btn:hover {
        background: #0f238d;
        color: white;
        transform: translateX(5px);
    }

    .nav-btn i {
        color: #ff9d00;
        transition: color 0.3s ease;
    }

    .nav-btn:hover i {
        color: white;
    }

    /* Newsletter Widget */
    .newsletter-widget {
        background: linear-gradient(135deg, #0f238d 0%, #ff9d00 100%);
        color: white;
    }

    .newsletter-widget .widget-title {
        color: white;
        border-bottom-color: rgba(255, 255, 255, 0.2);
    }

    .newsletter-widget .widget-title::after {
        background: white;
    }

    .newsletter-text {
        opacity: 0.9;
        line-height: 1.6;
        margin-bottom: 1.5rem;
    }

    .newsletter-form .form-control {
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.3);
        color: white;
        backdrop-filter: blur(10px);
    }

    .newsletter-form .form-control::placeholder {
        color: rgba(255, 255, 255, 0.7);
    }

    .newsletter-form .form-control:focus {
        background: rgba(255, 255, 255, 0.2);
        border-color: white;
        box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.25);
        color: white;
    }

    .newsletter-form .btn-primary {
        background: white;
        border-color: white;
        color: #0f238d;
        font-weight: 600;
    }

    .newsletter-form .btn-primary:hover {
        background: #f8f9fa;
        border-color: #f8f9fa;
        transform: translateY(-2px);
    }

    /* Post Navigation */
    .post-navigation {
        margin-top: 3rem;
        padding-top: 2rem;
        border-top: 2px solid #f0f0f0;
    }

    .nav-container {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 2rem;
    }

    .nav-item {
        background: #f8f9fa;
        border-radius: 15px;
        overflow: hidden;
        transition: all 0.3s ease;
        border: 1px solid #e9ecef;
    }

    .nav-item:hover {
        background: #e9ecef;
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    }

    .nav-item.prev-post {
        text-align: left;
    }

    .nav-item.next-post {
        text-align: right;
        grid-column: 2;
    }

    .nav-item:only-child {
        grid-column: 1 / -1;
        text-align: center;
    }

    .nav-link {
        display: block;
        padding: 1.5rem;
        text-decoration: none;
        color: inherit;
    }

    .nav-direction {
        font-size: 0.9rem;
        color: #666;
        font-weight: 600;
        margin-bottom: 0.5rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .nav-title {
        font-size: 1.1rem;
        font-weight: 700;
        color: #0f238d;
        line-height: 1.3;
        transition: color 0.3s ease;
    }

    .nav-item:hover .nav-title {
        color: #ff9d00;
    }

    /* Responsive Design */
    @media (max-width: 1199px) {
        .hero-title {
            font-size: 2.5rem;
        }

        .blog-article {
            padding: 2rem;
        }

        .sidebar-widget {
            padding: 1.5rem;
        }
    }

    @media (max-width: 991px) {
        .hero-title {
            font-size: 2rem;
        }

        .post-meta-hero .meta-items {
            padding: 0.75rem 1.5rem;
            flex-direction: column;
            gap: 0.5rem;
        }

        .blog-sidebar {
            position: static;
            margin-top: 3rem;
        }

        .share-buttons-grid {
            grid-template-columns: repeat(2, 1fr);
        }

        .form-grid {
            grid-template-columns: 1fr;
        }
    }

    @media (max-width: 767px) {
        .hero-title {
            font-size: 1.75rem;
        }

        .blog-article {
            padding: 1.5rem;
            border-radius: 15px;
        }

        .post-meta-hero .meta-items {
            padding: 0.5rem 1rem;
        }

        .meta-item {
            font-size: 0.8rem;
        }

        .share-buttons-grid {
            grid-template-columns: 1fr;
        }

        .comment-item {
            flex-direction: column;
            text-align: center;
        }

        .comment-avatar .avatar-circle {
            align-self: center;
        }

        .comment-header {
            flex-direction: column;
            text-align: center;
            gap: 0.25rem;
        }

        .related-post-item {
            flex-direction: column;
            text-align: center;
        }

        .related-post-item .post-thumb {
            align-self: center;
            width: 100px;
            height: 100px;
        }

        .sidebar-widget {
            padding: 1.25rem;
        }
    }

    @media (max-width: 575px) {
        .hero-title {
            font-size: 1.5rem;
        }

        .quick-share {
            flex-direction: column;
            gap: 1rem;
        }

        .share-buttons {
            justify-content: center;
        }

        .blog-article {
            padding: 1rem;
        }

        .comment-form-section {
            padding: 1.5rem;
        }

        .submit-btn {
            width: 100%;
        }
    }

    /* Animation Classes */
    .fade-in {
        animation: fadeIn 0.6s ease-in-out;
    }

    @keyframes fadeIn {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* Scroll Progress Bar */
    .scroll-progress {
        position: fixed;
        top: 0;
        left: 0;
        width: 0%;
        height: 3px;
        background: linear-gradient(90deg, #0f238d, #ff9d00);
        z-index: 9999;
        transition: width 0.1s ease;
    }

    /* Copy Link Success */
    .copy-success {
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: #28a745;
        color: white;
        padding: 1rem 2rem;
        border-radius: 10px;
        z-index: 10000;
        animation: copySuccess 2s ease-in-out;
    }

    @keyframes copySuccess {
        0%, 100% { opacity: 0; transform: translate(-50%, -50%) scale(0.8); }
        20%, 80% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
    }
</style>

<!-- JavaScript for Enhanced Functionality -->
<script>
    // Copy to clipboard functionality
    function copyToClipboard(text) {
        navigator.clipboard.writeText(text).then(function() {
            // Show success message
            const successMsg = document.createElement('div');
            successMsg.className = 'copy-success';
            successMsg.textContent = 'Link copied to clipboard!';
            document.body.appendChild(successMsg);

            setTimeout(() => {
                document.body.removeChild(successMsg);
            }, 2000);
        }).catch(function(err) {
            console.error('Could not copy text: ', err);
            // Fallback for older browsers
            const textArea = document.createElement('textarea');
            textArea.value = text;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);

            const successMsg = document.createElement('div');
            successMsg.className = 'copy-success';
            successMsg.textContent = 'Link copied to clipboard!';
            document.body.appendChild(successMsg);

            setTimeout(() => {
                document.body.removeChild(successMsg);
            }, 2000);
        });
    }

    // Scroll progress bar
    document.addEventListener('DOMContentLoaded', function() {
        // Create progress bar
        const progressBar = document.createElement('div');
        progressBar.className = 'scroll-progress';
        document.body.appendChild(progressBar);

        // Update progress on scroll
        window.addEventListener('scroll', function() {
            const scrollTop = window.pageYOffset;
            const docHeight = document.body.scrollHeight - window.innerHeight;
            const scrollPercent = (scrollTop / docHeight) * 100;
            progressBar.style.width = scrollPercent + '%';
        });

        // Add fade-in animation to elements
        const animatedElements = document.querySelectorAll('.blog-article, .sidebar-widget, .comment-item');
        animatedElements.forEach((element, index) => {
            element.style.animationDelay = `${index * 0.1}s`;
            element.classList.add('fade-in');
        });

        // Smooth scroll for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Enhanced form submission
        const commentForm = document.querySelector('.modern-comment-form');
        if (commentForm) {
            commentForm.addEventListener('submit', function(e) {
                const submitBtn = this.querySelector('.submit-btn');
                const originalText = submitBtn.innerHTML;

                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Posting...';
                submitBtn.disabled = true;

                // Form will submit normally, this is just for UX
                setTimeout(() => {
                    submitBtn.innerHTML = originalText;
                    submitBtn.disabled = false;
                }, 3000);
            });
        }

        // Newsletter form enhancement
        const newsletterForm = document.querySelector('.newsletter-form');
        if (newsletterForm) {
            newsletterForm.addEventListener('submit', function(e) {
                e.preventDefault();
                const submitBtn = this.querySelector('.btn-primary');
                const originalText = submitBtn.innerHTML;

                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Subscribing...';
                submitBtn.disabled = true;

                setTimeout(() => {
                    submitBtn.innerHTML = '<i class="fas fa-check me-2"></i>Subscribed!';
                    setTimeout(() => {
                        submitBtn.innerHTML = originalText;
                        submitBtn.disabled = false;
                        this.reset();
                    }, 2000);
                }, 1500);
            });
        }

        // Lazy loading for images
        if ('IntersectionObserver' in window) {
            const imageObserver = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        img.src = img.dataset.src;
                        img.classList.remove('lazy');
                        imageObserver.unobserve(img);
                    }
                });
            });

            document.querySelectorAll('img[data-src]').forEach(img => {
                imageObserver.observe(img);
            });
        }
    });

    // Reading progress and estimated time
    document.addEventListener('DOMContentLoaded', function() {
        const article = document.querySelector('.post-text');
        if (article) {
            const words = article.textContent.trim().split(/\s+/).length;
            const readingTime = Math.ceil(words / 200); // Average reading speed

            // Update any reading time displays
            document.querySelectorAll('.reading-time').forEach(el => {
                el.textContent = readingTime + ' min read';
            });
        }
    });
</script>

{% include 'users/footer.html' %}
{% endblock %}